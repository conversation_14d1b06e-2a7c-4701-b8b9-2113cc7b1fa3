import { DisplayItem } from "@/components/DisplayItem";
import { cn } from "@/lib/utils";
import { safeTimestampToNumber, hasTimestampExpired, getMillisecondsUntilTimestamp } from "@/utils/timestampHelpers";
import { Clock, Hammer, RefreshCw, XCircle, Package2 } from "lucide-react";
import useCancelCraft from "../api/useCancelCraft";
import useCollectCraft from "../api/useCollectCraft";
import useStartCraft from "../api/useStartCraft";
import type { CraftingQueueItem } from "../types/crafting";

export default function CraftingQueue({
    craftingQueue,
    maxCraftQueue,
    isLoading,
    isError,
}: {
    craftingQueue: CraftingQueueItem[];
    maxCraftQueue: number;
    isLoading: boolean;
    isError: boolean;
}) {
    // Use the collect craft hook
    const { mutate: collectCraft, isPending: isCollecting } = useCollectCraft();

    // Use the start craft hook
    const { mutate: startCraft, isPending: isStarting } = useStartCraft();

    // Use the cancel craft hook
    const { mutate: cancelCraft, isPending: isCancelling } = useCancelCraft();

    // Check if a crafting item is completed
    const isCompleted = (item: CraftingQueueItem) => {
        return hasTimestampExpired(item?.endsAt);
    };

    // Format time remaining
    const formatTimeRemaining = (endsAt: string | bigint | number | undefined | null) => {
        const timeLeftMs = getMillisecondsUntilTimestamp(endsAt);

        if (timeLeftMs === -1) return "Unknown";
        if (timeLeftMs <= 0) return "Ready";

        const hours = Math.floor(timeLeftMs / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeftMs % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeftMs % (1000 * 60)) / 1000);

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    };

    // Calculate progress percentage
    const getProgressPercentage = (item: CraftingQueueItem) => {
        const startTime = safeTimestampToNumber(item?.startedAt);
        const endTime = safeTimestampToNumber(item?.endsAt);

        if (startTime === 0 || endTime === 0) return 0;

        const now = Date.now();
        const totalTime = endTime - startTime;
        const elapsed = now - startTime;

        if (totalTime <= 0) return 100;
        return Math.min(100, Math.max(0, (elapsed / totalTime) * 100));
    };

    // Collect and recraft function
    const handleCollectAndRecraft = (item: CraftingQueueItem) => {
        const recipeId = item?.crafting_recipe.id;
        const itemId = item?.id;

        if (!recipeId || !itemId) {
            console.error("Missing recipe ID or item ID for collect and recraft");
            return;
        }

        collectCraft(
            { id: itemId },
            {
                onSuccess: () => {
                    // Start crafting the same recipe again
                    startCraft({ recipeId, amount: 1 });
                },
            }
        );
    };

    return (
        <div className="card-body p-0">
            {/* Header */}
            <div className="bg-base-100">
                <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/20 rounded-lg">
                            <Package2 className="size-5 text-primary" />
                        </div>
                        <div>
                            <h3 className="font-display text-base">Crafting Queue</h3>
                            {!isLoading && !isError && (
                                <div className="flex items-center gap-2">
                                    <span className="text-xs text-base-content/60">
                                        {craftingQueue.length}/{maxCraftQueue} Slots
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="px-2 pb-4">
                    {isLoading && (
                        <div className="flex items-center justify-center py-8">
                            <span className="loading loading-spinner loading-md text-primary"></span>
                        </div>
                    )}

                    {isError && (
                        <div className="alert alert-error">
                            <XCircle className="size-5" />
                            <span>Failed to load crafting queue</span>
                        </div>
                    )}

                    {!isLoading && !isError && craftingQueue.length > 0 ? (
                        <div className="space-y-2">
                            {craftingQueue.map((item) => {
                                const completed = isCompleted(item);
                                const outputItem = item?.crafting_recipe.outputs[0];
                                const progress = getProgressPercentage(item);

                                return (
                                    <div
                                        key={item?.id}
                                        className={cn(
                                            "card bg-base-200 border transition-all",
                                            completed ? "border-success shadow-md shadow-success/10" : "border-base-300"
                                        )}
                                    >
                                        <div className="card-body p-2">
                                            <div className="flex items-center gap-2">
                                                {/* Item Image */}
                                                <div className="indicator">
                                                    <div className="avatar">
                                                        <div className="w-10 rounded bg-base-300 p-1">
                                                            {outputItem?.image ? (
                                                                <DisplayItem item={outputItem} className="" />
                                                            ) : (
                                                                <div className="text-xl flex items-center justify-center h-full">
                                                                    📦
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Item Info and Progress */}
                                                <div className="flex-1 min-w-0">
                                                    <div>
                                                        <h4 className="font-medium text-sm truncate">
                                                            {outputItem?.name || "Unknown Item"}
                                                            {outputItem && outputItem?.amount > 1 && (
                                                                <span className="ml-1 text-green-400 font-bold">
                                                                    x{outputItem?.amount}
                                                                </span>
                                                            )}
                                                        </h4>
                                                        {completed ? (
                                                            <p className="text-green-400 text-xs">
                                                                Ready to collect
                                                            </p>
                                                        ) : (
                                                            <div className="flex items-center gap-2 mt-0.5">
                                                                <div className="flex items-center gap-1 text-xs text-base-content/70">
                                                                    <Clock className="size-3 text-info" />
                                                                    {formatTimeRemaining(item?.endsAt)}
                                                                </div>
                                                                {/* Progress Bar */}
                                                                <div className="flex-1">
                                                                    <progress
                                                                        className="progress progress-info h-1.5 w-full"
                                                                        value={progress}
                                                                        max="100"
                                                                    ></progress>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>

                                                {/* Actions */}
                                                <div className="flex items-center gap-1">
                                                    {completed ? (
                                                        <>
                                                            <button
                                                                disabled={isCollecting || isStarting}
                                                                title="Collect and recraft"
                                                                className={cn(
                                                                    "btn btn-info btn-sm btn-square",
                                                                    (isCollecting || isStarting) && "loading"
                                                                )}
                                                                onClick={() => handleCollectAndRecraft(item)}
                                                            >
                                                                {!(isCollecting || isStarting) && (
                                                                    <RefreshCw color="black" className="size-3" />
                                                                )}
                                                            </button>
                                                            <button
                                                                disabled={isCollecting || isStarting}
                                                                className={cn(
                                                                    "btn btn-success btn-sm",
                                                                    (isCollecting || isStarting) && "loading"
                                                                )}
                                                                onClick={() => {
                                                                    if (item?.id) {
                                                                        collectCraft({ id: item.id });
                                                                    }
                                                                }}
                                                            >
                                                                {!isCollecting && (
                                                                    <Package2 color="black" className="size-3 " />
                                                                )}
                                                                <span className="inline text-gray-900">Collect</span>
                                                            </button>
                                                        </>
                                                    ) : (
                                                        <button
                                                            disabled={isCancelling}
                                                            className={cn(
                                                                "btn btn-error btn-outline btn-xs",
                                                                isCancelling && "loading"
                                                            )}
                                                            onClick={() => {
                                                                if (item?.id) {
                                                                    cancelCraft({ id: item.id });
                                                                }
                                                            }}
                                                        >
                                                            {!isCancelling && <XCircle className="size-3" />}
                                                            <span className="hidden sm:inline">Cancel</span>
                                                        </button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    ) : !isLoading && !isError ? (
                        <div className="flex flex-col items-center justify-center py-4">
                            <div className="mb-3">
                                <Hammer className="size-8 text-base-content/50" />
                            </div>
                            <p className="text-base-content/70">No items in queue</p>
                        </div>
                    ) : null}
                </div>
            </div>
        </div>
    );
}
